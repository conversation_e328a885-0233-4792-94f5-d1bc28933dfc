{"name": "@hapi/vision", "description": "Templates rendering plugin support for hapi.js", "version": "7.0.3", "repository": "git://github.com/hapijs/vision", "main": "lib/index.js", "types": "lib/index.d.ts", "files": ["lib"], "keywords": ["view", "render", "template", "hapi"], "eslintConfig": {"extends": ["plugin:@hapi/module"]}, "dependencies": {"@hapi/boom": "^10.0.1", "@hapi/bounce": "^3.0.1", "@hapi/hoek": "^11.0.2", "@hapi/validate": "^2.0.1"}, "devDependencies": {"@hapi/code": "^9.0.3", "@hapi/eslint-plugin": "^6.0.0", "@hapi/hapi": "^21.2.1", "@hapi/lab": "^25.1.2", "@types/node": "^14.18.48", "babel-core": "^6.0.0", "babel-plugin-transform-react-jsx": "^6.0.0", "coveralls": "^3.0.0", "ejs": "^3.1.3", "handlebars": "^4.5.3", "hapi-react-views": "^10.0.0", "joi": "^17.9.2", "marko": "^5.21.2", "mustache": "^4.0.1", "nunjucks": "^3.0.0", "pug": "^3.0.0", "react": "^16.0.0", "react-dom": "^16.0.0", "twig": "^1.0.0", "typescript": "^5.1.3"}, "scripts": {"test": "lab -a @hapi/code -t 100 -L -I __core-js_shared__ -m 5000 -Y", "test-cov-html": "lab -a @hapi/code -r html -o coverage.html"}, "license": "BSD-3-<PERSON><PERSON>"}